### Test HexaGrid Web API

### Get API Info
GET http://localhost:5136/

### Get Available Node Types
GET http://localhost:5136/api/graph/node-types

### Test Graph Execution
POST http://localhost:5136/api/graph/run
Content-Type: application/json

{
  "Name": "Test Graph",
  "Description": "A simple test graph",
  "Nodes": [
    {
      "Id": "1",
      "Type": "WaitNode",
      "Name": "Wait 1 Second",
      "Inputs": {
        "duration": 1
      },
      "Outputs": {},
      "Position": { "X": 100, "Y": 100 }
    },
    {
      "Id": "2",
      "Type": "PrintNode",
      "Name": "Print Success",
      "Inputs": {
        "message": "Graph executed successfully!"
      },
      "Outputs": {},
      "Position": { "X": 300, "Y": 100 }
    }
  ],
  "Connections": [
    {
      "FromNodeId": "1",
      "FromPort": "done",
      "ToNodeId": "2",
      "ToPort": "trigger"
    }
  ],
  "Variables": {
    "testVar": "Hello World"
  }
}

### Test Graph Validation
POST http://localhost:5136/api/graph/validate
Content-Type: application/json

{
  "Name": "Invalid Graph",
  "Nodes": [
    {
      "Id": "1",
      "Type": "UnknownNode",
      "Inputs": {},
      "Outputs": {}
    },
    {
      "Id": "1",
      "Type": "PrintNode",
      "Inputs": {},
      "Outputs": {}
    }
  ],
  "Connections": [
    {
      "FromNodeId": "1",
      "FromPort": "output",
      "ToNodeId": "999",
      "ToPort": "input"
    }
  ]
}

### Test Graph from JSON String
POST http://localhost:5136/api/graph/run-from-json
Content-Type: application/json

"{\"Nodes\":[{\"Id\":\"1\",\"Type\":\"PrintNode\",\"Inputs\":{\"message\":\"Hello from JSON!\"}}],\"Connections\":[]}"
