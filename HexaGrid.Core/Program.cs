﻿using HexaGrid.Core;
using HexaGrid.Core.Nodes;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

class Program
{
    static async Task Main()
    {
        var context = new Dictionary<string, object>();

        List<INode> nodes =
        [
            new PrintNode(),
            new WaitNode(),
            new PrintNode()
        ];

        foreach (var node in nodes)
        {
            Console.WriteLine($"▶ Running: {node.Name}");
            await node.ExecuteAsync(context);
        }

        Console.WriteLine("✅ Workflow done.");
    }
}