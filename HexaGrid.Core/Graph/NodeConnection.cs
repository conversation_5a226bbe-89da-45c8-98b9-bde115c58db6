namespace HexaGrid.Core.Graph
{
    public class NodeConnection
    {
        public string FromNodeId { get; set; } = string.Empty;
        public string FromPort { get; set; } = string.Empty;
        public string ToNodeId { get; set; } = string.Empty;
        public string ToPort { get; set; } = string.Empty;

        public NodeConnection()
        {
        }

        public NodeConnection(string fromNodeId, string fromPort, string toNodeId, string toPort)
        {
            FromNodeId = fromNodeId;
            FromPort = fromPort;
            ToNodeId = toNodeId;
            ToPort = toPort;
        }

        public override string ToString()
        {
            return $"{FromNodeId}.{FromPort} -> {ToNodeId}.{ToPort}";
        }
    }
}