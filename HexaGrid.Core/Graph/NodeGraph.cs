using HexaGrid.Core.Models;
using System.Collections.Generic;
using System.Linq;

namespace HexaGrid.Core.Graph
{
    public class NodeGraph
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<Node> Nodes { get; set; } = new();
        public List<NodeConnection> Connections { get; set; } = new();
        public Dictionary<string, object> Variables { get; set; } = new();

        public NodeGraph()
        {
        }

        public NodeGraph(string name)
        {
            Name = name;
        }

        public void AddNode(Node node)
        {
            if (Nodes.Any(n => n.Id == node.Id))
                throw new InvalidOperationException($"Node with ID '{node.Id}' already exists in the graph.");

            Nodes.Add(node);
        }

        public void RemoveNode(string nodeId)
        {
            var node = Nodes.FirstOrDefault(n => n.Id == nodeId);
            if (node != null)
            {
                Nodes.Remove(node);
                // Remove all connections involving this node
                Connections.RemoveAll(c => c.FromNodeId == nodeId || c.ToNodeId == nodeId);
            }
        }

        public void AddConnection(NodeConnection connection)
        {
            // Validate that both nodes exist
            if (!Nodes.Any(n => n.Id == connection.FromNodeId))
                throw new InvalidOperationException($"Source node '{connection.FromNodeId}' not found in graph.");

            if (!Nodes.Any(n => n.Id == connection.ToNodeId))
                throw new InvalidOperationException($"Target node '{connection.ToNodeId}' not found in graph.");

            // Check for duplicate connections
            if (Connections.Any(c => c.FromNodeId == connection.FromNodeId &&
                                   c.FromPort == connection.FromPort &&
                                   c.ToNodeId == connection.ToNodeId &&
                                   c.ToPort == connection.ToPort))
                throw new InvalidOperationException("Connection already exists.");

            Connections.Add(connection);
        }

        public void RemoveConnection(NodeConnection connection)
        {
            Connections.RemoveAll(c => c.FromNodeId == connection.FromNodeId &&
                                     c.FromPort == connection.FromPort &&
                                     c.ToNodeId == connection.ToNodeId &&
                                     c.ToPort == connection.ToPort);
        }

        public Node? GetNode(string nodeId)
        {
            return Nodes.FirstOrDefault(n => n.Id == nodeId);
        }

        public List<NodeConnection> GetIncomingConnections(string nodeId)
        {
            return Connections.Where(c => c.ToNodeId == nodeId).ToList();
        }

        public List<NodeConnection> GetOutgoingConnections(string nodeId)
        {
            return Connections.Where(c => c.FromNodeId == nodeId).ToList();
        }

        public List<Node> GetExecutionOrder()
        {
            var visited = new HashSet<string>();
            var result = new List<Node>();
            var visiting = new HashSet<string>();

            foreach (var node in Nodes)
            {
                if (!visited.Contains(node.Id))
                {
                    TopologicalSort(node.Id, visited, visiting, result);
                }
            }

            return result;
        }

        private void TopologicalSort(string nodeId, HashSet<string> visited, HashSet<string> visiting, List<Node> result)
        {
            if (visiting.Contains(nodeId))
                throw new InvalidOperationException("Circular dependency detected in graph.");

            if (visited.Contains(nodeId))
                return;

            visiting.Add(nodeId);

            var incomingConnections = GetIncomingConnections(nodeId);
            foreach (var connection in incomingConnections)
            {
                TopologicalSort(connection.FromNodeId, visited, visiting, result);
            }

            visiting.Remove(nodeId);
            visited.Add(nodeId);

            var node = GetNode(nodeId);
            if (node != null)
                result.Add(node);
        }
    }
}