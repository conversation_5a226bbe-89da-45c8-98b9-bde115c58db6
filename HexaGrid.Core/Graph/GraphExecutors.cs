using HexaGrid.Core.Models;
using HexaGrid.Core.Nodes;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HexaGrid.Core.Graph
{
    public class GraphExecutor
    {
        private readonly INodeFactory _nodeFactory;

        public GraphExecutor(INodeFactory nodeFactory)
        {
            _nodeFactory = nodeFactory;
        }

        public static async Task<GraphExecutionResult> Execute(NodeGraph graph)
        {
            var executor = new GraphExecutor(new DefaultNodeFactory());
            return await executor.ExecuteAsync(graph);
        }

        public async Task<GraphExecutionResult> ExecuteAsync(NodeGraph graph)
        {
            var result = new GraphExecutionResult();
            var context = new Dictionary<string, object>(graph.Variables);

            try
            {
                var executionOrder = graph.GetExecutionOrder();

                foreach (var nodeDefinition in executionOrder)
                {
                    var nodeInstance = _nodeFactory.CreateNode(nodeDefinition.Type);
                    if (nodeInstance == null)
                    {
                        result.Success = false;
                        result.ErrorMessage = $"Unknown node type: {nodeDefinition.Type}";
                        return result;
                    }

                    // Configure node with inputs from connections and node definition
                    ConfigureNodeInputs(nodeInstance, nodeDefinition, graph, context);

                    // Execute the node
                    var nodeResult = await ExecuteNodeAsync(nodeInstance, context);

                    result.NodeResults[nodeDefinition.Id] = nodeResult;

                    if (!nodeResult.Success)
                    {
                        result.Success = false;
                        result.ErrorMessage = $"Node {nodeDefinition.Id} failed: {nodeResult.ErrorMessage}";
                        return result;
                    }

                    // Update context with node outputs
                    foreach (var output in nodeResult.Outputs)
                    {
                        context[$"{nodeDefinition.Id}.{output.Key}"] = output.Value;
                    }
                }

                result.Success = true;
                result.FinalContext = context;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        private void ConfigureNodeInputs(INode nodeInstance, Node nodeDefinition, NodeGraph graph, Dictionary<string, object> context)
        {
            // Set inputs from node definition
            if (nodeInstance is IConfigurableNode configurableNode)
            {
                configurableNode.Configure(nodeDefinition.Inputs);
            }

            // Set inputs from connections
            var incomingConnections = graph.GetIncomingConnections(nodeDefinition.Id);
            foreach (var connection in incomingConnections)
            {
                var sourceKey = $"{connection.FromNodeId}.{connection.FromPort}";
                if (context.ContainsKey(sourceKey))
                {
                    if (nodeInstance is IConfigurableNode configurable)
                    {
                        configurable.SetInput(connection.ToPort, context[sourceKey]);
                    }
                }
            }
        }

        private async Task<NodeResult> ExecuteNodeAsync(INode node, Dictionary<string, object> context)
        {
            try
            {
                await node.ExecuteAsync(context);

                var outputs = new Dictionary<string, object>();
                if (node is IOutputNode outputNode)
                {
                    outputs = outputNode.GetOutputs();
                }

                return NodeResult.CreateSuccess(outputs);
            }
            catch (Exception ex)
            {
                return NodeResult.CreateError(ex.Message);
            }
        }
    }

    public class GraphExecutionResult
    {
        public bool Success { get; set; } = true;
        public string? ErrorMessage { get; set; }
        public Dictionary<string, NodeResult> NodeResults { get; set; } = new();
        public Dictionary<string, object> FinalContext { get; set; } = new();
    }
}