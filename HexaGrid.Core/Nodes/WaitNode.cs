using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class WaitNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "WaitNode";

        private int _duration = 2000; // Default 2 seconds
        private readonly Dictionary<string, object> _outputs = new();

        public async Task ExecuteAsync(Dictionary<string, object> context)
        {
            Console.WriteLine($"⏳ Waiting {_duration / 1000.0} seconds...");

            var startTime = DateTime.UtcNow;
            await Task.Delay(_duration);
            var endTime = DateTime.UtcNow;

            // Set outputs
            _outputs["done"] = true;
            _outputs["duration"] = _duration;
            _outputs["actualDuration"] = (endTime - startTime).TotalMilliseconds;
            _outputs["startTime"] = startTime;
            _outputs["endTime"] = endTime;
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("duration", out var duration))
            {
                if (duration is int intDuration)
                    _duration = intDuration * 1000; // Convert seconds to milliseconds
                else if (duration is double doubleDuration)
                    _duration = (int)(doubleDuration * 1000);
                else if (int.TryParse(duration.ToString(), out var parsedDuration))
                    _duration = parsedDuration * 1000;
            }
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "duration":
                    if (value is int intDuration)
                        _duration = intDuration * 1000;
                    else if (value is double doubleDuration)
                        _duration = (int)(doubleDuration * 1000);
                    else if (int.TryParse(value?.ToString(), out var parsedDuration))
                        _duration = parsedDuration * 1000;
                    break;
                case "trigger":
                    // Trigger input doesn't change behavior, just allows connection
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}