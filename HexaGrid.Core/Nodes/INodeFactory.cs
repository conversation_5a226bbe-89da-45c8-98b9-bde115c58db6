using System.Collections.Generic;

namespace HexaGrid.Core.Nodes
{
    public interface INodeFactory
    {
        INode? CreateNode(string nodeType);
        IEnumerable<string> GetAvailableNodeTypes();
    }

    public interface IConfigurableNode
    {
        void Configure(Dictionary<string, object> inputs);
        void SetInput(string portName, object value);
    }

    public interface IOutputNode
    {
        Dictionary<string, object> GetOutputs();
    }

    public class DefaultNodeFactory : INodeFactory
    {
        private readonly Dictionary<string, Func<INode>> _nodeCreators;

        public DefaultNodeFactory()
        {
            _nodeCreators = new Dictionary<string, Func<INode>>
            {
                { "PrintNode", () => new PrintNode() },
                { "WaitNode", () => new WaitNode() }
            };
        }

        public INode? CreateNode(string nodeType)
        {
            return _nodeCreators.TryGetValue(nodeType, out var creator) ? creator() : null;
        }

        public IEnumerable<string> GetAvailableNodeTypes()
        {
            return _nodeCreators.Keys;
        }

        public void RegisterNodeType<T>(string nodeType) where T : INode, new()
        {
            _nodeCreators[nodeType] = () => new T();
        }

        public void RegisterNodeType(string nodeType, Func<INode> creator)
        {
            _nodeCreators[nodeType] = creator;
        }
    }
}
