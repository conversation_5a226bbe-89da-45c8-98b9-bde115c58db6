using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class PrintNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "PrintNode";

        private string _message = "👋 Hello from PrintNode!";
        private readonly Dictionary<string, object> _outputs = new();

        public Task ExecuteAsync(Dictionary<string, object> context)
        {
            Console.WriteLine(_message);

            // Set outputs
            _outputs["printed"] = _message;
            _outputs["timestamp"] = DateTime.UtcNow;

            return Task.CompletedTask;
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("message", out var message))
            {
                _message = message?.ToString() ?? _message;
            }
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "message":
                    _message = value?.ToString() ?? _message;
                    break;
                case "trigger":
                    // Trigger input doesn't change behavior, just allows connection
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}