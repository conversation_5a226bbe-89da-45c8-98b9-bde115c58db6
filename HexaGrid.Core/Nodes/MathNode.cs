using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class MathNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "MathNode";
        
        private double _valueA = 0;
        private double _valueB = 0;
        private string _operation = "add";
        private readonly Dictionary<string, object> _outputs = new();

        public Task ExecuteAsync(Dictionary<string, object> context)
        {
            double result = _operation.ToLower() switch
            {
                "add" => _valueA + _valueB,
                "subtract" => _valueA - _valueB,
                "multiply" => _valueA * _valueB,
                "divide" => _valueB != 0 ? _valueA / _valueB : double.NaN,
                "power" => Math.Pow(_valueA, _valueB),
                "min" => Math.Min(_valueA, _valueB),
                "max" => Math.Max(_valueA, _valueB),
                _ => double.NaN
            };

            Console.WriteLine($"🔢 Math: {_valueA} {_operation} {_valueB} = {result}");
            
            _outputs["result"] = result;
            _outputs["valueA"] = _valueA;
            _outputs["valueB"] = _valueB;
            _outputs["operation"] = _operation;
            _outputs["isValid"] = !double.IsNaN(result);
            
            return Task.CompletedTask;
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("valueA", out var valueA))
                _valueA = Convert.ToDouble(valueA);
            
            if (inputs.TryGetValue("valueB", out var valueB))
                _valueB = Convert.ToDouble(valueB);
            
            if (inputs.TryGetValue("operation", out var operation))
                _operation = operation?.ToString() ?? "add";
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "valuea":
                case "a":
                    _valueA = Convert.ToDouble(value);
                    break;
                case "valueb":
                case "b":
                    _valueB = Convert.ToDouble(value);
                    break;
                case "operation":
                case "op":
                    _operation = value?.ToString() ?? "add";
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}
