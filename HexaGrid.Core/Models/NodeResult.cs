using System.Collections.Generic;

namespace HexaGrid.Core.Models
{
    public class NodeResult
    {
        public bool Success { get; set; } = true;
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Outputs { get; set; } = new();
        public Dictionary<string, object> Context { get; set; } = new();

        public static NodeResult CreateSuccess(Dictionary<string, object>? outputs = null)
        {
            return new NodeResult
            {
                Success = true,
                Outputs = outputs ?? new Dictionary<string, object>()
            };
        }

        public static NodeResult CreateError(string errorMessage)
        {
            return new NodeResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }
}