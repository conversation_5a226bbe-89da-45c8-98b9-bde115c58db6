using System.Collections.Generic;

namespace HexaGrid.Core.Models
{
    public class Node
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> Inputs { get; set; } = new();
        public Dictionary<string, object> Outputs { get; set; } = new();
        public Dictionary<string, object> Properties { get; set; } = new();
        public Position Position { get; set; } = new();

        public Node()
        {
        }

        public Node(string id, string type)
        {
            Id = id;
            Type = type;
            Name = type; // Default name to type
        }
    }

    public class Position
    {
        public double X { get; set; }
        public double Y { get; set; }

        public Position() { }

        public Position(double x, double y)
        {
            X = x;
            Y = y;
        }
    }
}
