using System.Collections.Generic;

namespace HexaGrid.Core.Models
{
    public enum PortType
    {
        Input,
        Output
    }

    public class NodePort
    {
        public string Name { get; set; } = string.Empty;
        public PortType Type { get; set; }
        public Type DataType { get; set; } = typeof(object);
        public object? DefaultValue { get; set; }
        public bool IsRequired { get; set; } = false;
        public string Description { get; set; } = string.Empty;

        public NodePort(string name, PortType type, Type? dataType = null)
        {
            Name = name;
            Type = type;
            DataType = dataType ?? typeof(object);
        }
    }

    public class NodePortValue
    {
        public string PortName { get; set; } = string.Empty;
        public object? Value { get; set; }
        public Type ValueType { get; set; } = typeof(object);

        public NodePortValue(string portName, object? value = null)
        {
            PortName = portName;
            Value = value;
            ValueType = value?.GetType() ?? typeof(object);
        }

        public T? GetValue<T>()
        {
            if (Value is T typedValue)
                return typedValue;

            try
            {
                return (T?)Convert.ChangeType(Value, typeof(T));
            }
            catch
            {
                return default(T);
            }
        }
    }
}