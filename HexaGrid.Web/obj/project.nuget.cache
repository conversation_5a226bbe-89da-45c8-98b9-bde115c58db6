{"version": 2, "dgSpecHash": "Fn76jSjG47Y=", "success": true, "projectFilePath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/HexaGrid.Web.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/microsoft.extensions.apidescription.server/9.0.0/microsoft.extensions.apidescription.server.9.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.openapi/1.6.23/microsoft.openapi.1.6.23.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore/9.0.3/swashbuckle.aspnetcore.9.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/9.0.3/swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/9.0.3/swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/9.0.3/swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512"], "logs": []}