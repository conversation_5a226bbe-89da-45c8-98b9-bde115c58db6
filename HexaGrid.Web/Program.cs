using HexaGrid.Core.Nodes;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Register HexaGrid services
builder.Services.AddSingleton<INodeFactory, DefaultNodeFactory>();

// Add CORS for development
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseCors("AllowAll");
}

app.UseHttpsRedirection();
app.UseAuthorization();

app.MapControllers();

app.MapGet("/", () => new {
    message = "🔷 HexaGrid Web API",
    version = "1.0.0",
    endpoints = new[] {
        "/api/graph/run - POST: Execute a graph",
        "/api/graph/validate - POST: Validate a graph",
        "/swagger - API Documentation"
    }
});

app.Run();
