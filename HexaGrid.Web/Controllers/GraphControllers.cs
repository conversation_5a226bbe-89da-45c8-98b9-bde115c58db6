using Microsoft.AspNetCore.Mvc;
using HexaGrid.Core.Graph;
using HexaGrid.Core.Nodes;
using HexaGrid.Runtime;
using System.Text.Json;

namespace HexaGrid.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
public class GraphController : ControllerBase
{
    private readonly INodeFactory _nodeFactory;
    private readonly ILogger<GraphController> _logger;

    public GraphController(INodeFactory nodeFactory, ILogger<GraphController> logger)
    {
        _nodeFactory = nodeFactory;
        _logger = logger;
    }

    [HttpPost("run")]
    public async Task<IActionResult> RunGraph([FromBody] NodeGraph graph)
    {
        try
        {
            _logger.LogInformation("Executing graph: {GraphName} with {NodeCount} nodes",
                graph.Name, graph.Nodes.Count);

            var executor = new GraphExecutor(_nodeFactory);
            var result = await executor.ExecuteAsync(graph);

            if (result.Success)
            {
                _logger.LogInformation("Graph execution completed successfully");
                return Ok(new {
                    success = true,
                    result = result,
                    message = "Graph executed successfully"
                });
            }
            else
            {
                _logger.LogWarning("Graph execution failed: {ErrorMessage}", result.ErrorMessage);
                return BadRequest(new {
                    success = false,
                    error = result.ErrorMessage,
                    nodeResults = result.NodeResults
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing graph");
            return StatusCode(500, new {
                success = false,
                error = ex.Message
            });
        }
    }

    [HttpPost("run-from-json")]
    public async Task<IActionResult> RunGraphFromJson([FromBody] string graphJson)
    {
        try
        {
            var graphLoader = new GraphLoader();
            var graph = graphLoader.LoadFromJson(graphJson);

            return await RunGraph(graph);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Invalid JSON format");
            return BadRequest(new {
                success = false,
                error = "Invalid JSON format: " + ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading graph from JSON");
            return StatusCode(500, new {
                success = false,
                error = ex.Message
            });
        }
    }

    [HttpPost("validate")]
    public IActionResult ValidateGraph([FromBody] NodeGraph graph)
    {
        try
        {
            var validationResult = ValidateGraphStructure(graph);

            return Ok(new {
                valid = validationResult.IsValid,
                errors = validationResult.Errors,
                warnings = validationResult.Warnings
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating graph");
            return StatusCode(500, new {
                valid = false,
                error = ex.Message
            });
        }
    }

    [HttpGet("node-types")]
    public IActionResult GetAvailableNodeTypes()
    {
        try
        {
            var nodeTypes = _nodeFactory.GetAvailableNodeTypes();
            return Ok(new {
                nodeTypes = nodeTypes.ToArray(),
                count = nodeTypes.Count()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting node types");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    private GraphValidationResult ValidateGraphStructure(NodeGraph graph)
    {
        var result = new GraphValidationResult();

        // Check for empty graph
        if (!graph.Nodes.Any())
        {
            result.Warnings.Add("Graph contains no nodes");
            return result;
        }

        // Check for duplicate node IDs
        var duplicateIds = graph.Nodes.GroupBy(n => n.Id)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        foreach (var duplicateId in duplicateIds)
        {
            result.Errors.Add($"Duplicate node ID: {duplicateId}");
        }

        // Check for invalid connections
        foreach (var connection in graph.Connections)
        {
            if (!graph.Nodes.Any(n => n.Id == connection.FromNodeId))
            {
                result.Errors.Add($"Connection references non-existent source node: {connection.FromNodeId}");
            }

            if (!graph.Nodes.Any(n => n.Id == connection.ToNodeId))
            {
                result.Errors.Add($"Connection references non-existent target node: {connection.ToNodeId}");
            }
        }

        // Check for unknown node types
        var availableTypes = _nodeFactory.GetAvailableNodeTypes().ToHashSet();
        foreach (var node in graph.Nodes)
        {
            if (!availableTypes.Contains(node.Type))
            {
                result.Errors.Add($"Unknown node type: {node.Type} (Node ID: {node.Id})");
            }
        }

        // Check for circular dependencies
        try
        {
            graph.GetExecutionOrder();
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Circular dependency"))
        {
            result.Errors.Add("Circular dependency detected in graph");
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private class GraphValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }
}
