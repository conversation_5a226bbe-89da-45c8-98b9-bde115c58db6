using HexaGrid.Core.Graph;
using HexaGrid.Core.Models;
using System.Text.Json;

namespace HexaGrid.Runtime
{
    public class GraphLoader
    {
        private readonly JsonSerializerOptions _jsonOptions;

        public GraphLoader()
        {
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                WriteIndented = true
            };
        }

        public async Task<NodeGraph> LoadFromFileAsync(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Graph file not found: {filePath}");

            var jsonContent = await File.ReadAllTextAsync(filePath);
            return LoadFromJson(jsonContent);
        }

        public NodeGraph LoadFromFile(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Graph file not found: {filePath}");

            var jsonContent = File.ReadAllText(filePath);
            return LoadFrom<PERSON>son(jsonContent);
        }

        public NodeGraph LoadFromJson(string jsonContent)
        {
            try
            {
                var graphData = JsonSerializer.Deserialize<GraphData>(jsonContent, _jsonOptions);
                if (graphData == null)
                    throw new InvalidOperationException("Failed to deserialize graph data");

                return ConvertToNodeGraph(graphData);
            }
            catch (JsonException ex)
            {
                throw new InvalidOperationException($"Invalid JSON format: {ex.Message}", ex);
            }
        }

        public async Task SaveToFileAsync(NodeGraph graph, string filePath)
        {
            var graphData = ConvertFromNodeGraph(graph);
            var jsonContent = JsonSerializer.Serialize(graphData, _jsonOptions);
            await File.WriteAllTextAsync(filePath, jsonContent);
        }

        public void SaveToFile(NodeGraph graph, string filePath)
        {
            var graphData = ConvertFromNodeGraph(graph);
            var jsonContent = JsonSerializer.Serialize(graphData, _jsonOptions);
            File.WriteAllText(filePath, jsonContent);
        }

        private NodeGraph ConvertToNodeGraph(GraphData graphData)
        {
            var graph = new NodeGraph
            {
                Name = graphData.Name ?? "Untitled Graph",
                Description = graphData.Description ?? ""
            };

            // Add nodes
            foreach (var nodeData in graphData.Nodes ?? new List<NodeData>())
            {
                var node = new Node(nodeData.Id ?? Guid.NewGuid().ToString(), nodeData.Type ?? "Unknown")
                {
                    Name = nodeData.Name ?? nodeData.Type ?? "Unknown",
                    Description = nodeData.Description ?? "",
                    Inputs = nodeData.Inputs ?? new Dictionary<string, object>(),
                    Outputs = nodeData.Outputs ?? new Dictionary<string, object>(),
                    Properties = nodeData.Properties ?? new Dictionary<string, object>()
                };

                if (nodeData.Position != null)
                {
                    node.Position = new Position(nodeData.Position.X, nodeData.Position.Y);
                }

                graph.AddNode(node);
            }

            // Add connections
            foreach (var connectionData in graphData.Connections ?? new List<ConnectionData>())
            {
                var connection = new NodeConnection(
                    connectionData.FromNodeId ?? "",
                    connectionData.FromPort ?? "",
                    connectionData.ToNodeId ?? "",
                    connectionData.ToPort ?? ""
                );

                graph.AddConnection(connection);
            }

            // Add variables
            if (graphData.Variables != null)
            {
                foreach (var variable in graphData.Variables)
                {
                    graph.Variables[variable.Key] = variable.Value;
                }
            }

            return graph;
        }

        private GraphData ConvertFromNodeGraph(NodeGraph graph)
        {
            return new GraphData
            {
                Name = graph.Name,
                Description = graph.Description,
                Nodes = graph.Nodes.Select(n => new NodeData
                {
                    Id = n.Id,
                    Type = n.Type,
                    Name = n.Name,
                    Description = n.Description,
                    Inputs = n.Inputs,
                    Outputs = n.Outputs,
                    Properties = n.Properties,
                    Position = new PositionData { X = n.Position.X, Y = n.Position.Y }
                }).ToList(),
                Connections = graph.Connections.Select(c => new ConnectionData
                {
                    FromNodeId = c.FromNodeId,
                    FromPort = c.FromPort,
                    ToNodeId = c.ToNodeId,
                    ToPort = c.ToPort
                }).ToList(),
                Variables = graph.Variables
            };
        }
    }

    // Data transfer objects for JSON serialization
    public class GraphData
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public List<NodeData>? Nodes { get; set; }
        public List<ConnectionData>? Connections { get; set; }
        public Dictionary<string, object>? Variables { get; set; }
    }

    public class NodeData
    {
        public string? Id { get; set; }
        public string? Type { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public Dictionary<string, object>? Inputs { get; set; }
        public Dictionary<string, object>? Outputs { get; set; }
        public Dictionary<string, object>? Properties { get; set; }
        public PositionData? Position { get; set; }
    }

    public class ConnectionData
    {
        public string? FromNodeId { get; set; }
        public string? FromPort { get; set; }
        public string? ToNodeId { get; set; }
        public string? ToPort { get; set; }
    }

    public class PositionData
    {
        public double X { get; set; }
        public double Y { get; set; }
    }
}