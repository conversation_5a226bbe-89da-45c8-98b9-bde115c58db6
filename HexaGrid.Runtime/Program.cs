﻿using HexaGrid.Core.Graph;
using HexaGrid.Runtime;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🔷 HexaGrid Runtime");
        Console.WriteLine("==================");

        try
        {
            var graphLoader = new GraphLoader();

            // Check if a graph file was provided as argument
            string graphFile = args.Length > 0 ? args[0] : "../sample-graph.json";

            if (!File.Exists(graphFile))
            {
                Console.WriteLine($"❌ Graph file not found: {graphFile}");
                Console.WriteLine("Usage: HexaGrid.Runtime [graph-file.json]");
                return;
            }

            Console.WriteLine($"📂 Loading graph from: {graphFile}");
            var graph = await graphLoader.LoadFromFileAsync(graphFile);

            Console.WriteLine($"📊 Graph loaded: {graph.Name}");
            Console.WriteLine($"   Nodes: {graph.Nodes.Count}");
            Console.WriteLine($"   Connections: {graph.Connections.Count}");
            Console.WriteLine();

            Console.WriteLine("▶️ Executing graph...");
            var result = await GraphExecutor.Execute(graph);

            if (result.Success)
            {
                Console.WriteLine("✅ Graph execution completed successfully!");

                if (result.NodeResults.Any())
                {
                    Console.WriteLine("\n📋 Node Results:");
                    foreach (var nodeResult in result.NodeResults)
                    {
                        Console.WriteLine($"   {nodeResult.Key}: {(nodeResult.Value.Success ? "✅ Success" : "❌ Failed")}");
                        if (!nodeResult.Value.Success && !string.IsNullOrEmpty(nodeResult.Value.ErrorMessage))
                        {
                            Console.WriteLine($"      Error: {nodeResult.Value.ErrorMessage}");
                        }
                    }
                }
            }
            else
            {
                Console.WriteLine($"❌ Graph execution failed: {result.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"💥 Error: {ex.Message}");
            if (args.Contains("--verbose") || args.Contains("-v"))
            {
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
