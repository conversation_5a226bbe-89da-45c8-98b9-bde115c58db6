# HexaGrid

HexaGrid is a flexible node-based workflow execution system built with .NET. It allows you to create, connect, and execute nodes in a graph-based workflow.

## Project Structure

The solution consists of three main projects:

- **HexaGrid.Core**: Contains the core models, interfaces, and execution logic
- **HexaGrid.Runtime**: Command-line application for loading and executing graph definitions from JSON files
- **HexaGrid.Web**: Web API for executing and validating graphs via HTTP endpoints

## Getting Started

### Prerequisites

- .NET 10.0 or later

### Building the Solution

```bash
dotnet build
```

### Running the Runtime

```bash
cd HexaGrid.Runtime
dotnet run -- ../sample-graph.json
```

### Running the Web API

```bash
cd HexaGrid.Web
dotnet run
```

Then navigate to `https://localhost:7114` or `http://localhost:5136` to access the API.

## Core Concepts

### Nodes

Nodes are the basic building blocks of a workflow. Each node has:

- **Inputs**: Data that flows into the node
- **Outputs**: Data that flows out of the node
- **Execution Logic**: The actual work performed by the node

Built-in node types:

- **PrintNode**: Outputs a message to the console
- **WaitNode**: Waits for a specified duration

### Connections

Connections define how data flows between nodes. Each connection has:

- **Source Node and Port**: Where the data comes from
- **Target Node and Port**: Where the data goes to

### Graph

A graph is a collection of nodes and connections that define a workflow. Graphs can be:

- Created programmatically
- Loaded from JSON files
- Executed via the Runtime or Web API

## JSON Graph Format

```json
{
  "Nodes": [
    {
      "Id": "1",
      "Type": "WaitNode",
      "Inputs": {
        "duration": 2
      },
      "Outputs": {}
    },
    {
      "Id": "2",
      "Type": "PrintNode",
      "Inputs": {
        "message": "Wait completed!"
      },
      "Outputs": {}
    }
  ],
  "Connections": [
    {
      "FromNodeId": "1",
      "FromPort": "done",
      "ToNodeId": "2",
      "ToPort": "trigger"
    }
  ]
}
```

## Web API Endpoints

- `POST /api/graph/run`: Execute a graph
- `POST /api/graph/run-from-json`: Execute a graph from JSON string
- `POST /api/graph/validate`: Validate a graph structure
- `GET /api/graph/node-types`: Get available node types

## Extending HexaGrid

### Creating Custom Nodes

1. Create a class that implements `INode`
2. Optionally implement `IConfigurableNode` and `IOutputNode` interfaces
3. Register your node type with the `DefaultNodeFactory`

Example:

```csharp
public class MyCustomNode : INode, IConfigurableNode, IOutputNode
{
    public string Name => "MyCustomNode";
    
    private readonly Dictionary<string, object> _outputs = new();
    
    public Task ExecuteAsync(Dictionary<string, object> context)
    {
        // Your node logic here
        _outputs["result"] = "Custom node executed!";
        return Task.CompletedTask;
    }
    
    public void Configure(Dictionary<string, object> inputs)
    {
        // Configure your node from inputs
    }
    
    public void SetInput(string portName, object value)
    {
        // Handle dynamic input setting
    }
    
    public Dictionary<string, object> GetOutputs()
    {
        return new Dictionary<string, object>(_outputs);
    }
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
